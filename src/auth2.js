import axios from 'axios';
import databaseName from './myFunctions/databaseName';

// Base Auth Class - contains common authentication logic
class BaseAuth {
  constructor() {
    this.loading = false;
    this.currentUser = null;
  }

  async makeAuthRequest(endpoint, credentials) {
    console.log('🌐 Making auth request to:', endpoint);
    console.log('📧 Email:', credentials.email);

    try {
      const loginData = {
        "Username": credentials.email.toString(),
        "Password": databaseName.GeneratePasswordHash(credentials.password)
      };

      console.log('📤 Sending request with data:', { Username: loginData.Username, Password: '[HIDDEN]' });

      const response = await axios({
        method: 'POST',
        url: endpoint,
        data: loginData,
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('📥 Response status:', response.status);
      console.log('📥 Response data:', response.data);

      if (response.status === 500) {
        throw new Error(response.data.message);
      }

      return {
        isOk: true,
        data: response.data.data
      };

    } catch (error) {
      console.error('❌ Auth request error:', error);
      console.error('❌ Error response:', error.response);

      let errorMessage = "HTTP Error While Trying To Login";
      if (error.response?.status === 500) {
        errorMessage = error.response.data.message;
      } else if (error.message) {
        errorMessage = error.message;
      }

      return {
        isOk: false,
        message: errorMessage
      };
    }
  }

  createSessionData(userData, userType) {
    return {
      isOk: true,
      data: userData,
      userType: userType,
      timestamp: new Date().toISOString()
    };
  }

  saveToSession(sessionData) {
    sessionStorage.setItem("KOZ_SERVICE_AUTH", JSON.stringify(sessionData));
  }

  getFromSession() {
    try {
      const sessionData = sessionStorage.getItem("KOZ_SERVICE_AUTH");
      return sessionData ? JSON.parse(sessionData) : null;
    } catch (error) {
      console.error('Error parsing session data:', error);
      return null;
    }
  }

  clearSession() {
    sessionStorage.removeItem("KOZ_SERVICE_AUTH");
    this.currentUser = null;
  }
}

// Unified Authentication Class
class UnifiedAuth extends BaseAuth {
  constructor(userType) {
    super();
    this.userType = userType;
    this.endpoint = userType === 'customer' ? 'api/Auth/CustomerLogin' : 'api/Auth/SIMLogin';
  }

  createUnifiedUserObject(apiData, userType) {
    // Create unified user object with generic field names
    const unifiedUser = {
      // Generic fields
      first_name: userType === 'internal' ? apiData.sim_first_name : apiData.customers_first_name,
      last_name: userType === 'internal' ? apiData.sim_last_name : apiData.customers_last_name,
      email: userType === 'internal' ? apiData.sim_email : apiData.customers_email,
      company_name: userType === 'internal' ? apiData.sim_company_name : apiData.customer_name,
      office_phone: userType === 'internal' ? apiData.sim_office_phone_number : apiData.customers_office_phone_number,
      cell_phone: userType === 'internal' ? apiData.sim_cell_phone_number : apiData.customers_cell_phone_number,
      username: userType === 'internal' ? apiData.sim_email : apiData.customers_username,
      security_level: userType === 'internal' ? apiData.sim_user_security : 'customer',
      auth_token: apiData.auth_token,
      user_type: userType,
      idcustomers: userType === 'internal' ? null : apiData.idcustomers,


      // Maintain backward compatibility - include original field names
      ...(userType === 'internal' ? {
        sim_first_name: apiData.sim_first_name,
        sim_last_name: apiData.sim_last_name,
        sim_company_name: apiData.sim_company_name,
        sim_office_phone_number: apiData.sim_office_phone_number,
        sim_cell_phone_number: apiData.sim_cell_phone_number,
        sim_user_security: apiData.sim_user_security,
        sim_email: apiData.sim_email
      } : {
        customers_first_name: apiData.customers_first_name,
        customers_last_name: apiData.customers_last_name,
        customer_name: apiData.customer_name,
        customers_username: apiData.customers_username,
        customers_email: apiData.customers_email,
        customers_cell_phone_number: apiData.customers_cell_phone_number,
        customers_office_phone_number: apiData.customers_office_phone_number,
        idcustomers: apiData.idcustomers
      })
    };

    return unifiedUser;
  }

  async login(credentials) {
    const result = await this.makeAuthRequest(this.endpoint, credentials);

    if (result.isOk) {
      const userData = this.createUnifiedUserObject(result.data, this.userType);

      this.currentUser = userData;
      const sessionData = this.createSessionData(userData, this.userType);
      this.saveToSession(sessionData);

      return {
        isOk: true,
        data: userData
      };
    }

    return result;
  }

  restoreFromSession(sessionData) {
    if (sessionData && sessionData.userType === this.userType) {
      // Check if the session data already has unified fields
      if (sessionData.data.first_name && sessionData.data.last_name) {
        // Already unified, use as-is
        this.currentUser = sessionData.data;
      } else {
        // Old format, need to convert to unified format
        this.currentUser = this.createUnifiedUserObject(sessionData.data, this.userType);
      }
      return true;
    }
    return false;
  }
}

// Main Authentication Manager
class AuthManager {
  constructor() {
    this.currentAuth = null;
    this.baseAuth = new BaseAuth(); // For session management

    // Try to restore session on initialization
    this.restoreSession();
  }

  async logIn(email, password, userType) {
    console.log('🔐 AuthManager.logIn called with:', { email, userType });

    if (!userType) {
      console.log('❌ No userType provided');
      return {
        isOk: false,
        message: 'Please select a login type'
      };
    }

    const credentials = { email, password };

    // Create a new UnifiedAuth instance for the specified user type
    const auth = new UnifiedAuth(userType);
    console.log(`🔐 Using Unified Auth with endpoint: ${auth.endpoint}`);

    const result = await auth.login(credentials);

    if (result.isOk) {
      this.currentAuth = auth;
    }

    return result;
  }

  async logOut() {
    if (this.currentAuth) {
      this.currentAuth.clearSession();
    }
    this.currentAuth = null;
  }

  loggedIn() {
    return !!(this.currentAuth && this.currentAuth.currentUser);
  }

  async getUser() {
    if (this.currentAuth && this.currentAuth.currentUser) {
      // Ensure the user object has unified fields
      const userData = this.currentAuth.currentUser;

      // If it doesn't have unified fields, recreate it
      if (!userData.first_name && (userData.sim_first_name || userData.customers_first_name)) {
        console.log('🔄 Converting user data to unified format...');
        this.currentAuth.currentUser = this.currentAuth.createUnifiedUserObject(userData, this.currentAuth.userType);

        // Update session storage with unified format
        const sessionData = this.currentAuth.createSessionData(this.currentAuth.currentUser, this.currentAuth.userType);
        this.currentAuth.saveToSession(sessionData);
      }

      return {
        isOk: true,
        data: this.currentAuth.currentUser
      };
    }

    // Try to restore from session
    if (this.restoreSession()) {
      return {
        isOk: true,
        data: this.currentAuth.currentUser
      };
    }

    return {
      isOk: false,
      message: 'No user logged in'
    };
  }

  getUserType() {
    return this.currentAuth ? this.currentAuth.userType : null;
  }

  isCustomer() {
    return this.getUserType() === 'customer';
  }

  isInternal() {
    return this.getUserType() === 'internal';
  }

  restoreSession() {
    const sessionData = this.baseAuth.getFromSession();

    if (sessionData && sessionData.userType) {
      // Create appropriate auth instance based on stored user type
      const auth = new UnifiedAuth(sessionData.userType);

      if (auth.restoreFromSession(sessionData)) {
        this.currentAuth = auth;
        return true;
      }
    }

    return false;
  }
}

// Export singleton instance
export default new AuthManager();
